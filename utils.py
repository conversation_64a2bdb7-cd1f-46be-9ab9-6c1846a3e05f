import tiktoken
import logging
from typing import Dict, List, Any, Union
from datetime import datetime
from pathlib import Path
import mimetypes
import base64
import os
import json
import time
import logging
import base64
import threading
from datetime import datetime
from typing import List, Dict, Optional, Any
from config import get_timestamp, load_config

def estimate_tokens(text: str, model: str = "gpt-4") -> int:
    """精确计算文本的token数量"""
    if not text:
        return 0
    
    try:
        # 获取对应模型的编码器
        if "gpt-4" in model or "gpt-3.5" in model:
            encoding = tiktoken.encoding_for_model("gpt-4")
        elif "claude" in model.lower():
            # Claude系列使用类似GPT-4的编码
            encoding = tiktoken.encoding_for_model("gpt-4")
        elif "gemini" in model.lower():
            # Gemini系列也使用类似的编码
            encoding = tiktoken.encoding_for_model("gpt-4")
        else:
            # 默认使用GPT-4编码
            encoding = tiktoken.encoding_for_model("gpt-4")
        
        # 精确计算token数
        tokens = encoding.encode(text)
        return len(tokens)
        
    except Exception as e:
        print(f"Token计算失败: {e}")
        # 如果tiktoken失败，使用简单估算作为后备
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars
        estimated_tokens = (chinese_chars // 2) + (other_chars // 4)
        return max(1, estimated_tokens)

def calculate_messages_tokens(messages: List[dict], model: str = "gpt-4") -> int:
    """计算消息列表的总token数（包括格式化开销）"""
    if not messages:
        return 0
    
    try:
        # 获取编码器
        if "gpt-4" in model or "gpt-3.5" in model:
            encoding = tiktoken.encoding_for_model("gpt-4")
        else:
            encoding = tiktoken.encoding_for_model("gpt-4")
        
        total_tokens = 0
        
        for message in messages:
            # 每条消息的基础开销（role + content 的格式化）
            total_tokens += 4  # 每条消息约4个token的开销
            
            # 计算角色的token
            if message.get('role'):
                total_tokens += len(encoding.encode(message['role']))
            
            # 计算内容的token - 修复这里的问题
            if message.get('content'):
                content = message['content']
                if isinstance(content, str):
                    total_tokens += len(encoding.encode(content))
                elif isinstance(content, list):
                    # 如果是数组格式，提取文本部分
                    text_content = extract_text_from_content(content)
                    if text_content:
                        total_tokens += len(encoding.encode(text_content))
                else:
                    # 其他情况转换为字符串
                    text_content = str(content)
                    total_tokens += len(encoding.encode(text_content))
        
        # 整个对话的格式化开销
        total_tokens += 2  # 对话开始和结束的开销
        
        return total_tokens
        
    except Exception as e:
        print(f"消息token计算失败: {e}")
        # 后备方案：简单累加每条消息的内容
        total = 0
        for msg in messages:
            content = msg.get('content', '')
            if isinstance(content, str):
                total += estimate_tokens(content, model)
            elif isinstance(content, list):
                text_content = extract_text_from_content(content)
                total += estimate_tokens(text_content, model)
            else:
                total += estimate_tokens(str(content), model)
        return total + len(messages) * 4  # 加上格式开销

def trim_context_messages(messages: List[dict], max_turns: int = 10, max_tokens: int = 8000, model: str = "gpt-4", max_messages: int = None) -> List[dict]:
    """
    裁剪上下文消息，保持在限制范围内
    
    Args:
        messages: 消息列表
        max_turns: 最大轮次（一问一答算一轮）
        max_tokens: 最大token数
        model: 模型名称，用于精确计算token
        max_messages: 最大消息条数（优先级高于max_turns）
    
    Returns:
        裁剪后的消息列表
    """
    if not messages:
        return messages
    
    # 检测并保护总结消息（总结消息通常成对出现：用户"总结以往对话" + AI回复）
    summary_pairs = []
    protected_messages = []
    
    for i, msg in enumerate(messages):
        if (msg.get('role') == 'user' and 
            isinstance(msg.get('content'), str) and 
            msg.get('content').strip() == '总结以往对话'):
            # 找到总结用户消息，查找下一条AI回复
            if i + 1 < len(messages) and messages[i + 1].get('role') == 'assistant':
                summary_pairs.append((i, i + 1))
                protected_messages.extend([i, i + 1])
                print(f"[{get_timestamp()}] 检测到总结消息对: 索引 {i}-{i+1}")
    
    # 分离系统消息、保护的总结消息和其他对话消息
    system_messages = [msg for i, msg in enumerate(messages) if msg.get('role') == 'system']
    protected_summary_messages = [msg for i, msg in enumerate(messages) if i in protected_messages]
    conversation_messages = [msg for i, msg in enumerate(messages) if msg.get('role') != 'system' and i not in protected_messages]
    
    # 计算系统消息和受保护总结消息的token数
    system_tokens = calculate_messages_tokens(system_messages, model)
    protected_summary_tokens = calculate_messages_tokens(protected_summary_messages, model)
    
    print(f"[{get_timestamp()}] 系统消息token数: {system_tokens}")
    if protected_summary_messages:
        print(f"[{get_timestamp()}] 受保护总结消息token数: {protected_summary_tokens}")
    
    # 为对话留出token空间（减去系统消息和受保护总结消息的token）
    available_tokens = max_tokens - system_tokens - protected_summary_tokens
    
    # 如果系统消息和总结消息就已经超过限制，只保留系统消息
    if available_tokens <= 0:
        total_protected_tokens = system_tokens + protected_summary_tokens
        print(f"[{get_timestamp()}] 警告：系统消息和总结消息token数({total_protected_tokens})超过限制({max_tokens})")
        if protected_summary_tokens > 0:
            print(f"[{get_timestamp()}] 保留系统消息和总结消息")
            return system_messages + protected_summary_messages
        else:
            return system_messages
    
    # 按轮次限制（从最新的开始保留）
    if max_messages is not None and max_messages > 0:
        # 如果指定了最大消息条数，优先使用消息条数限制
        if len(conversation_messages) > max_messages:
            original_count = len(conversation_messages)
            conversation_messages = conversation_messages[-max_messages:]
            print(f"按消息条数裁剪：从{original_count}条消息保留最近{len(conversation_messages)}条")
    elif len(conversation_messages) > max_turns * 2:  # 每轮包含用户和助手消息
        original_count = len(conversation_messages)
        conversation_messages = conversation_messages[-(max_turns * 2):]
        print(f"按轮次裁剪：从{original_count}条消息保留最近{len(conversation_messages)}条")
    
    # 按token数限制（从最新的开始计算）
    trimmed_conversation = []
    current_tokens = 0
    
    for msg in reversed(conversation_messages):
        msg_tokens = calculate_messages_tokens([msg], model)
        if current_tokens + msg_tokens <= available_tokens:
            trimmed_conversation.insert(0, msg)
            current_tokens += msg_tokens
        else:
            print(f"按token数裁剪：已达到token限制({available_tokens})，当前使用{current_tokens}")
            break
    
    # 确保对话的连贯性（如果第一条是助手消息，尝试找到对应的用户消息）
    if trimmed_conversation and trimmed_conversation[0].get('role') == 'assistant':
        # 尝试在原始对话中找到前一条用户消息
        first_assistant_index = None
        for i, msg in enumerate(conversation_messages):
            if msg == trimmed_conversation[0]:
                first_assistant_index = i
                break
        
        if first_assistant_index and first_assistant_index > 0:
            prev_msg = conversation_messages[first_assistant_index - 1]
            if prev_msg.get('role') == 'user':
                prev_tokens = calculate_messages_tokens([prev_msg], model)
                if current_tokens + prev_tokens <= available_tokens:
                    trimmed_conversation.insert(0, prev_msg)
                    current_tokens += prev_tokens
                    print("添加了对应的用户消息以保持对话连贯性")
    
    # 组合最终消息：系统消息 + 受保护的总结消息 + 裁剪后的对话消息
    # 注意：总结消息应该在最近对话之前，以保持时间顺序
    final_messages = system_messages + protected_summary_messages + trimmed_conversation
    final_tokens = system_tokens + protected_summary_tokens + current_tokens
    
    total_final_messages = len(final_messages)
    total_protected = len(system_messages) + len(protected_summary_messages)
    
    print(f"[{get_timestamp()}] 上下文裁剪完成：{total_final_messages}条消息（{total_protected}条受保护），精确计算{final_tokens}个token")
    
    return final_messages

def extract_text_from_content(content) -> str:
    """从content中提取文本内容（处理字符串和数组格式）"""
    if isinstance(content, str):
        return content
    elif isinstance(content, list):
        # 从多媒体数组中提取文本部分
        text_parts = []
        for item in content:
            if isinstance(item, dict) and item.get('type') == 'text':
                text_parts.append(item.get('text', ''))
        return ' '.join(text_parts)
    else:
        return str(content) if content else ''

def get_character_pricing(model_id: str, config: dict, message_count: int = 0) -> dict:
    """
    获取角色的计费配置，支持阶段计费
    Args:
        model_id: 模型ID（实际使用的角色标识符）
        config: 配置对象
        message_count: 当前对话消息数（用于阶段计费）
    Returns:
        计费配置字典
    """
    try:
        # 检查是否启用角色独立计费
        pricing_config = config.get('pricing_config', {})
        enable_character_pricing = pricing_config.get('enable_character_independent_pricing', True)
        
        if not enable_character_pricing:
            # 如果未启用角色独立计费，回退到模型计费
            model_configs = config.get('model_config', {})
            model_config = model_configs.get(model_id, {})
            return model_config.get('pricing', {})
        
        # 通过model_id在character_roles中查找对应的角色
        character_roles = config.get('character_roles', {})
        character = None
        
        # 遍历所有角色，找到model_id匹配的角色
        for char_key, char_data in character_roles.items():
            if char_data.get('model_id') == model_id:
                character = char_data
                break
        
        if not character or 'pricing' not in character:
            # 如果角色无独立计费配置，根据设置决定是否回退
            fallback_enabled = pricing_config.get('fallback_to_model_pricing', True)
            if fallback_enabled:
                model_configs = config.get('model_config', {})
                model_config = model_configs.get(model_id, {})
                pricing = model_config.get('pricing', {})
                if pricing:
                    return pricing
                else:
                    print(f"[{get_timestamp()}] 警告：角色 {model_id} 无计费配置，回退失败")
                    return {}
            else:
                return {}
        
        character_pricing = character['pricing']
        
        # 检查是否启用独立计费
        if not character_pricing.get('enable_independent_pricing', True):
            # 如果角色禁用了独立计费，回退到模型计费
            model_configs = config.get('model_config', {})
            model_config = model_configs.get(model_id, {})
            pricing = model_config.get('pricing', {})
            return pricing
        
        pricing_mode = character_pricing.get('pricing_mode', 'simple')
        
        if pricing_mode == 'staged':
            # 阶段计费：根据消息数确定当前阶段
            staged_config = character_pricing.get('staged_pricing', {})
            if staged_config.get('enable_staged', False):
                stages = staged_config.get('stages', [])
                
                for stage in stages:
                    max_messages = stage.get('max_messages', -1)
                    if max_messages == -1 or message_count <= max_messages:
                        # 找到匹配的阶段
                        stage_pricing = {
                            'input_price_1m': stage.get('input_price_1m', 1.0),
                            'output_price_1m': stage.get('output_price_1m', 5.0),
                            'stage_name': stage.get('stage_name', '当前阶段')
                        }
                        return stage_pricing
                
                # 如果没有匹配的阶段，使用最后一个阶段
                if stages:
                    last_stage = stages[-1]
                    stage_pricing = {
                        'input_price_1m': last_stage.get('input_price_1m', 1.0),
                        'output_price_1m': last_stage.get('output_price_1m', 5.0),
                        'stage_name': last_stage.get('stage_name', '当前阶段')
                    }
                    return stage_pricing
        
        elif pricing_mode == 'tiered':
            # 阶梯计费：返回阶梯定价配置
            tiered_pricing = character_pricing.get('tiered_pricing', {})
            return tiered_pricing
        
        # 默认使用简单计费
        simple_pricing = character_pricing.get('simple_pricing', {})
        return simple_pricing
        
    except Exception as e:
        logging.error(f"获取角色计费配置失败: {e}")
        return {}

def calculate_conversation_cost(messages: List[dict], model_id: str = "astra") -> dict:
    """
    计算整个对话的费用 - 模拟API调用的累积计费
    按照实际API调用过程：每次请求包含历史+系统提示词，响应只计算当次输出
    现在支持每条消息使用不同的模型计费
    """
    
    try:
        config = load_config()
        
        if not config:
            config = {}
            
        pricing_config = config.get('pricing_config', {})
        if not pricing_config:
            pricing_config = {}
    except Exception as e:
        logging.error(f"[{get_timestamp()}] 费用计算 - 加载配置失败: {str(e)}")
        return {
            "total_cost_usd": 0.0,
            "total_cost_cny": 0.0,
            "input_cost_usd": 0.0,
            "output_cost_usd": 0.0,
            "input_tokens": 0,
            "output_tokens": 0,
            "requests": 0,
            "details": []
        }
    
    usd_to_cny = pricing_config.get('usd_to_cny_rate', 7.3)
    
    # 初始化系统提示词tokens缓存
    if not _prompt_tokens_cache:
        load_prompt_tokens_cache()
    
    total_input_cost = 0.0
    total_output_cost = 0.0
    total_input_tokens = 0
    total_output_tokens = 0
    request_count = 0
    cost_details = []
    
    # 模拟对话的API调用过程
    conversation_history = []
    
    # 用于跟踪当前请求使用的模型
    current_request_model = model_id  # 默认模型作为后备
    
    for i, message in enumerate(messages):
        if not message or not isinstance(message, dict):
            continue
            
        if message.get('role') == 'user':
            # 用户消息 - 准备下一次API请求
            conversation_history.append(message)
            
            # 查找下一条助手消息来确定使用的模型
            next_assistant_msg = None
            for next_msg in messages[i+1:]:
                if next_msg.get('role') == 'assistant':
                    next_assistant_msg = next_msg
                    break
            
            # 确定这次请求使用的模型
            if next_assistant_msg and next_assistant_msg.get('model_id'):
                current_request_model = next_assistant_msg.get('model_id')
            else:
                current_request_model = model_id  # 使用默认模型
            
            # 获取角色的计费配置（支持阶段计费）
            user_message_count = sum(1 for msg in conversation_history if msg.get('role') == 'user')
            pricing = get_character_pricing(current_request_model, config, user_message_count)
            
            if not pricing:
                print(f"[{get_timestamp()}] 🚨 严重错误：角色 {current_request_model} 无计费配置，跳过用户请求费用计算")
                continue
            
            # 计算请求输入：历史对话 + 系统提示词
            request_messages = conversation_history.copy()
            
            # 动态获取当前模型的系统提示词tokens
            model_system_prompt_tokens = get_model_system_prompt_tokens(current_request_model, config)
            
            # 计算输入tokens
            input_tokens = model_system_prompt_tokens  # 系统提示词
            
            for j, msg in enumerate(request_messages):
                if not msg or not isinstance(msg, dict):
                    continue
                content_text = extract_text_from_content(msg.get('content', ''))
                msg_tokens = estimate_tokens(content_text, current_request_model)
                input_tokens += msg_tokens
            
            # 计算输入费用（考虑阶梯定价）
            if 'threshold_tokens' in pricing and input_tokens > pricing['threshold_tokens']:
                # 超过阈值，使用高价
                input_price_per_1m = pricing.get('input_price_1m_high', pricing.get('input_price_1m', 0))
            else:
                # 普通价格
                input_price_per_1m = pricing.get('input_price_1m', 0)
            
            input_cost = (input_tokens / 1_000_000) * input_price_per_1m
            
            total_input_cost += input_cost
            total_input_tokens += input_tokens
            request_count += 1
            
            # 记录这次请求的详情
            cost_details.append({
                "request_id": request_count,
                "model_id": current_request_model,
                "input_tokens": input_tokens,
                "input_cost_usd": input_cost,
                "output_tokens": 0,
                "output_cost_usd": 0.0,
                "message_type": "user_request"
            })
            
        elif message.get('role') == 'assistant':
            # AI回复消息 - 计算输出费用
            conversation_history.append(message)
            
            # 获取这条AI消息使用的模型
            assistant_model = message.get('model_id', model_id)
            
            # 获取角色的计费配置（支持阶段计费）
            assistant_message_count = sum(1 for msg in conversation_history if msg.get('role') == 'assistant')
            pricing = get_character_pricing(assistant_model, config, assistant_message_count)
            
            if not pricing:
                print(f"[{get_timestamp()}] 🚨 严重错误：角色 {assistant_model} 无计费配置，跳过助手输出费用计算")
                continue
            
            # 计算输出tokens（包括思考过程）
            content_text = extract_text_from_content(message.get('content', ''))
            reasoning_text = message.get('reasoning', '')
            
            content_tokens = estimate_tokens(content_text, assistant_model)
            reasoning_tokens = 0
            if reasoning_text:
                reasoning_tokens = estimate_tokens(reasoning_text, assistant_model)
            
            output_tokens = content_tokens + reasoning_tokens
            
            # 计算输出费用（考虑阶梯定价）
            if 'threshold_tokens' in pricing and output_tokens > pricing['threshold_tokens']:
                output_price_per_1m = pricing.get('output_price_1m_high', pricing.get('output_price_1m', 0))
            else:
                output_price_per_1m = pricing.get('output_price_1m', 0)
            
            output_cost = (output_tokens / 1_000_000) * output_price_per_1m
            
            total_output_cost += output_cost
            total_output_tokens += output_tokens
            
            # 更新最后一个请求的输出信息
            if cost_details:
                total_request_cost = cost_details[-1]["input_cost_usd"] + output_cost
                cost_details[-1].update({
                    "output_tokens": output_tokens,
                    "output_cost_usd": output_cost,
                    "total_cost_usd": total_request_cost,
                    "assistant_model_id": assistant_model
                })
    
    total_cost_usd = total_input_cost + total_output_cost
    total_cost_cny = total_cost_usd * usd_to_cny
    
    # 只在出现问题时报告
    if total_cost_usd == 0 and len(messages) > 0:
        print(f"[{get_timestamp()}] 🚨 警告：总费用为0，可能存在计费配置问题！")
        print(f"  - 使用的模型ID: {model_id}")
        print(f"  - 总消息数: {len(messages)}")
        print(f"  - API请求数: {request_count}")
    
    result = {
        "total_cost_usd": round(total_cost_usd, 6),
        "total_cost_cny": round(total_cost_cny, 4),
        "input_cost_usd": round(total_input_cost, 6),
        "output_cost_usd": round(total_output_cost, 6),
        "input_tokens": total_input_tokens,
        "output_tokens": total_output_tokens,
        "requests": request_count,
        "details": cost_details
    }
    
    return result

def image_to_base64(file_path: str, content_type: str = None, data_url: bool = True) -> str:
    """将图片文件转换为base64格式
    
    Args:
        file_path: 图片文件路径
        content_type: MIME类型
        data_url: 是否返回完整的数据URL格式，False则返回纯base64数据
    """
    try:
        # 根据文件名或content_type确定正确的MIME类型
        if content_type and content_type.startswith('image/'):
            mime_type = content_type
        else:
            # 根据文件扩展名推断MIME类型
            file_extension = os.path.splitext(file_path)[1].lower()
            mime_mapping = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.webp': 'image/webp',
                '.bmp': 'image/bmp'
            }
            mime_type = mime_mapping.get(file_extension, 'image/jpeg')
        
        # 读取文件并转换为base64
        with open(file_path, 'rb') as f:
            image_data = f.read()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            if data_url:
                return f"data:{mime_type};base64,{base64_data}"
            else:
                return base64_data
            
    except Exception as e:
        print(f"图片转换为base64失败: {e}")
        return None

# 添加系统提示词tokens缓存
PROMPT_TOKENS_CACHE_FILE = "prompt_tokens_cache.json"
_prompt_tokens_cache = {}
_cache_lock = threading.Lock()

def load_prompt_tokens_cache() -> Dict[str, int]:
    """加载系统提示词tokens缓存"""
    global _prompt_tokens_cache
    
    try:
        if os.path.exists(PROMPT_TOKENS_CACHE_FILE):
            with open(PROMPT_TOKENS_CACHE_FILE, 'r', encoding='utf-8') as f:
                _prompt_tokens_cache = json.load(f)
                print(f"[{get_timestamp()}] 加载系统提示词tokens缓存: {len(_prompt_tokens_cache)} 个条目")
        else:
            _prompt_tokens_cache = {}
            print(f"[{get_timestamp()}] 系统提示词tokens缓存文件不存在，创建新缓存")
        
        return _prompt_tokens_cache
    except Exception as e:
        print(f"[{get_timestamp()}] 加载系统提示词tokens缓存失败: {e}")
        _prompt_tokens_cache = {}
        return _prompt_tokens_cache

def save_prompt_tokens_cache():
    """保存系统提示词tokens缓存"""
    try:
        with open(PROMPT_TOKENS_CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump(_prompt_tokens_cache, f, ensure_ascii=False, indent=2)
        print(f"[{get_timestamp()}] 保存系统提示词tokens缓存: {len(_prompt_tokens_cache)} 个条目")
    except Exception as e:
        print(f"[{get_timestamp()}] 保存系统提示词tokens缓存失败: {e}")

def calculate_prompt_tokens(prompt_content: str, model: str = "gpt-4") -> int:
    """计算单个提示词的tokens数"""
    try:
        # 使用现有的estimate_tokens函数
        tokens = estimate_tokens(prompt_content, model)
        return tokens
    except Exception as e:
        print(f"[{get_timestamp()}] 计算提示词tokens失败: {e}")
        # 使用简单估算：中文字符/2 + 英文字符/4
        chinese_chars = sum(1 for char in prompt_content if '\u4e00' <= char <= '\u9fff')
        other_chars = len(prompt_content) - chinese_chars
        return max(1, (chinese_chars // 2) + (other_chars // 4))

def update_all_prompt_tokens_cache(config: dict = None):
    """更新所有系统提示词的tokens缓存"""
    global _prompt_tokens_cache
    
    if not config:
        config = load_config()
    
    if not config:
        print(f"[{get_timestamp()}] 无法加载配置，跳过系统提示词tokens缓存更新")
        return
    
    with _cache_lock:
        system_prompts = config.get('system_prompts', {})
        updated_count = 0
        
        print(f"[{get_timestamp()}] 开始更新系统提示词tokens缓存...")
        
        for prompt_id, prompt_data in system_prompts.items():
            try:
                if not isinstance(prompt_data, dict):
                    continue
                    
                prompt_content = prompt_data.get('content', '')
                if not prompt_content:
                    continue
                
                # 计算tokens
                tokens = calculate_prompt_tokens(prompt_content)
                
                # 检查是否需要更新
                old_tokens = _prompt_tokens_cache.get(prompt_id, 0)
                if old_tokens != tokens:
                    _prompt_tokens_cache[prompt_id] = tokens
                    updated_count += 1
                    print(f"[{get_timestamp()}] 更新提示词 '{prompt_id}' tokens: {old_tokens} -> {tokens}")
                
            except Exception as e:
                print(f"[{get_timestamp()}] 处理提示词 '{prompt_id}' 失败: {e}")
        
        # 清理已删除的提示词
        cached_prompt_ids = set(_prompt_tokens_cache.keys())
        current_prompt_ids = set(system_prompts.keys())
        removed_prompts = cached_prompt_ids - current_prompt_ids
        
        for prompt_id in removed_prompts:
            del _prompt_tokens_cache[prompt_id]
            print(f"[{get_timestamp()}] 清理已删除的提示词缓存: {prompt_id}")
            updated_count += 1
        
        if updated_count > 0:
            save_prompt_tokens_cache()
            print(f"[{get_timestamp()}] 系统提示词tokens缓存更新完成，共更新 {updated_count} 个条目")
        else:
            print(f"[{get_timestamp()}] 系统提示词tokens缓存无需更新")

def get_model_system_prompt_tokens(model_id: str, config: dict = None) -> int:
    """获取指定模型的系统提示词tokens数"""
    global _prompt_tokens_cache
    
    if not config:
        config = load_config()
    
    if not config:
        print(f"[{get_timestamp()}] 无法加载配置，系统提示词tokens: 0")
        return 0
    
    try:
        # 查找角色对应的系统提示词ID
        character_roles = config.get('character_roles', {})
        
        # 根据model_id查找角色
        character = None
        for char_data in character_roles.values():
            if char_data.get('model_id') == model_id:
                character = char_data
                break
        
        if not character:
            print(f"[{get_timestamp()}] 未找到模型 {model_id} 对应的角色，系统提示词tokens: 0")
            return 0
        
        system_prompt_id = character.get('system_prompt_id')
        if not system_prompt_id:
            print(f"[{get_timestamp()}] 角色 {model_id} 未设置系统提示词ID，tokens: 0")
            return 0
        
        # 从缓存中获取tokens数
        if not _prompt_tokens_cache:
            load_prompt_tokens_cache()
        
        tokens = _prompt_tokens_cache.get(system_prompt_id, 0)
        
        if tokens <= 0:
            # 如果缓存中没有，尝试实时计算
            system_prompts = config.get('system_prompts', {})
            prompt_data = system_prompts.get(system_prompt_id, {})
            prompt_content = prompt_data.get('content', '')
            
            if prompt_content:
                tokens = calculate_prompt_tokens(prompt_content, model_id)
                # 更新缓存
                with _cache_lock:
                    _prompt_tokens_cache[system_prompt_id] = tokens
                    save_prompt_tokens_cache()
                print(f"[{get_timestamp()}] 实时计算并缓存系统提示词 '{system_prompt_id}' tokens: {tokens}")
            else:
                print(f"[{get_timestamp()}] 系统提示词 '{system_prompt_id}' 内容为空，tokens: 0")
                return 0
        

        return tokens
        
    except Exception as e:
        print(f"[{get_timestamp()}] 获取模型 {model_id} 系统提示词tokens失败: {e}")
        return 0

 